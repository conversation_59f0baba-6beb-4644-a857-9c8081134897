﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Linq;
using System.Transactions;

namespace Business.Concrete
{
    public class MembershipManager : IMembershipService
    {
        IMembershipDal _membershipDal;
        IPaymentDal _paymentDal;
        IRemainingDebtDal _remainingDebtDal;
        IMembershipFreezeHistoryService _freezeHistoryService;
        private readonly ICompanyContext _companyContext;

        public MembershipManager(IMembershipDal membershipDal,IPaymentDal paymentDal,IRemainingDebtDal remainingDebtDal, IMembershipFreezeHistoryService freezeHistoryService, ICompanyContext companyContext)
        {
            _membershipDal = membershipDal;
            _paymentDal = paymentDal;
            _remainingDebtDal = remainingDebtDal;
            _freezeHistoryService = freezeHistoryService;
            _companyContext = companyContext;
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Membership")]
        public IResult CancelFreeze(int membershipId)
        {
            // SOLID prensiplerine uygun: Entity manipülasyon ve history güncelleme DAL katmanına taşıdık
            return _membershipDal.CancelFreezeWithHistoryUpdate(membershipId, _companyContext.GetCompanyId());
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Membership")]
        public IResult ReactivateFromToday(int membershipId)
        {
            // SOLID prensiplerine uygun: Entity manipülasyon, tarih hesaplaması ve history güncelleme DAL katmanına taşıdık
            return _membershipDal.ReactivateFromTodayWithHistoryUpdate(membershipId, _companyContext.GetCompanyId());
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Membership")]
        public IResult FreezeMembership(MembershipFreezeRequestDto freezeRequest)
        {
            if (freezeRequest.FreezeDays < 1 || freezeRequest.FreezeDays > 365)
                return new ErrorResult(Messages.FreezeDaysInvalid);

            var remainingDaysResult = _freezeHistoryService.GetRemainingFreezeDays(freezeRequest.MembershipID);
            if (!remainingDaysResult.Success || remainingDaysResult.Data < freezeRequest.FreezeDays)
                return new ErrorResult("Yıllık dondurma hakkınız yetersiz");

            if (_membershipDal.IsMembershipFrozen(freezeRequest.MembershipID))
                return new ErrorResult(Messages.MembershipAlreadyFrozen);

            var freezeStartDate = DateTime.Now;
            // Bitiş tarihini gün olarak ayarla ve saati 00:01 olarak belirle
            var freezeEndDate = freezeStartDate.AddDays(freezeRequest.FreezeDays)
                .Date
                .AddHours(0)
                .AddMinutes(1);

            // Üyeliği dondur
            _membershipDal.FreezeMembership(freezeRequest.MembershipID, freezeRequest.FreezeDays);

            // Dondurma geçmişine kaydet
            var freezeHistory = new MembershipFreezeHistory
            {
                MembershipID = freezeRequest.MembershipID,
                StartDate = freezeStartDate,
                PlannedEndDate = freezeEndDate,
                FreezeDays = freezeRequest.FreezeDays,
                CreationDate = DateTime.Now
            };

            _freezeHistoryService.Add(freezeHistory);

            return new SuccessResult(Messages.MembershipFrozen);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Membership")]
        public IResult UnfreezeMembership(int membershipId)
        {
            if (!_membershipDal.IsMembershipFrozen(membershipId))
                return new ErrorResult(Messages.MembershipAlreadyFrozen);

            _membershipDal.UnfreezeMembership(membershipId);
            return new SuccessResult(Messages.MembershipUnfrozen);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 30, "Membership", "Frozen")]
        public IDataResult<List<MembershipFreezeDto>> GetFrozenMemberships()
        {
            return new SuccessDataResult<List<MembershipFreezeDto>>(_membershipDal.GetFrozenMemberships());
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Membership")]
        public IResult Add(MembershipAddDto membershipDto)
        {
            // SOLID prensiplerine uygun: Karmaşık ekleme işlemini DAL katmanına taşıdık
            return _membershipDal.AddMembershipWithPaymentAndDebt(membershipDto);
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Membership")]
        public IResult Update(MembershipUpdateDto membershipDto)
        {
            // SOLID prensiplerine uygun: Entity manipülasyon ve tarih yönetimini DAL katmanına taşıdık
            return _membershipDal.UpdateMembershipWithDateManagement(membershipDto, _companyContext.GetCompanyId());
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Membership")]
        public IResult Delete(int id)
        {
            // SOLID prensiplerine uygun: Karmaşık silme işlemini DAL katmanına taşıdık
            return _membershipDal.DeleteMembershipWithRelatedData(id, _companyContext.GetCompanyId());
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 30, "Membership", "All")]
        public IDataResult<List<Membership>> GetAll()
        {
            return new SuccessDataResult<List<Membership>>(_membershipDal.GetAll());
        }
        [SecuredOperation("owner,admin")]
        public IDataResult<List<Membership>> GetByMembershipId(int memberid)
        {
            return new SuccessDataResult<List<Membership>>(_membershipDal.GetAll(c => c.MembershipID == memberid));
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 30, "Membership", "LastInfo")]
        public IDataResult<LastMembershipInfoDto> GetLastMembershipInfo(int memberId)
        {
            // SOLID prensiplerine uygun: LINQ operations ve complex calculations DAL katmanına taşıdık
            return _membershipDal.GetLastMembershipInfoWithCalculations(memberId, _companyContext.GetCompanyId());
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<MembershipDetailForDeleteDto>> GetMemberActiveMemberships(int memberId)
        {
            return new SuccessDataResult<List<MembershipDetailForDeleteDto>>(_membershipDal.GetMemberActiveMemberships(memberId));
        }
    }
}
